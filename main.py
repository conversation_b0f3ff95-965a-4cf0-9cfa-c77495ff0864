from maix import image, display, app, time, camera
import cv2
import numpy as np
import math
from micu_uart_lib import (
    SimpleUART, micu_printf
)

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        lower_purple = np.array([90, 100, 180])
        upper_purple = np.array([180, 255, 255])
        mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        laser_points = []
        
        for cnt in contours_purple:
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            laser_points.append((cx, cy))
            cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)
            cv2.putText(img, "Laser", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        return img, laser_points

# --------------------------- 圆形轨迹点生成函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """在校正后的矩形内生成圆形轨迹点"""
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

# --------------------------- 串口数据包发送函数 ---------------------------
def send_laser_diff_packet(diff_x, diff_y):
    """
    发送激光点与圆心坐标差的二进制数据包
    数据包格式：0x78, 标识符, x高八位, x低八位, y高八位, y低八位, 0xFC

    标识符规则：
    - x为负，y为正：0xF0
    - x为正，y为负：0x0F
    - 都为负：0xFF
    - 都为正：0x00

    :param diff_x: x坐标差值（有符号整数）
    :param diff_y: y坐标差值（有符号整数）
    """
    # 判断符号并设置标识符
    x_negative = diff_x < 0
    y_negative = diff_y < 0

    if x_negative and y_negative:
        identifier = 0xFF  # 都为负
    elif x_negative and not y_negative:
        identifier = 0xF0  # x为负，y为正
    elif not x_negative and y_negative:
        identifier = 0x0F  # x为正，y为负
    else:
        identifier = 0x00  # 都为正

    # 取绝对值并限制在16位范围内
    diff_x_abs = abs(diff_x) & 0xFFFF
    diff_y_abs = abs(diff_y) & 0xFFFF

    # 分解为高低八位
    x_high = (diff_x_abs >> 8) & 0xFF  # x高八位
    x_low = diff_x_abs & 0xFF          # x低八位
    y_high = (diff_y_abs >> 8) & 0xFF  # y高八位
    y_low = diff_y_abs & 0xFF          # y低八位

    # 构建数据包
    packet = bytes([
        0x78,      # 包头
        identifier, # 标识符（符号信息）
        x_high,    # x坐标差绝对值高八位
        x_low,     # x坐标差绝对值低八位
        y_high,    # y坐标差绝对值高八位
        y_low,     # y坐标差绝对值低八位
        0xFC       # 包尾
    ])

    # 通过串口发送二进制数据包
    global uart
    if uart and uart.is_initialized:
        try:
            # 使用send_bytes方法，支持发送控制
            success = uart.send_bytes(packet)
            if success:
                print(f"发送数据包: diff_x={diff_x}, diff_y={diff_y}, 包=[{', '.join(f'0x{b:02X}' for b in packet)}]")
            else:
                print(f"数据包发送被阻止或失败")
        except Exception as e:
            print(f"数据包发送失败: {e}")
    else:
        print("串口未初始化，无法发送数据包")

def send_laser_fire_packet(diff_x, diff_y):
    """
    发送激光打开模式的二进制数据包（使用特殊标识符0x15）
    数据包格式：0x78, 0x15, x高八位, x低八位, y高八位, y低八位, 0xFC

    :param diff_x: x坐标差值（有符号整数）
    :param diff_y: y坐标差值（有符号整数）
    """
    # 取绝对值并限制在16位范围内
    diff_x_abs = abs(diff_x) & 0xFFFF
    diff_y_abs = abs(diff_y) & 0xFFFF

    # 分解为高低八位
    x_high = (diff_x_abs >> 8) & 0xFF  # x高八位
    x_low = diff_x_abs & 0xFF          # x低八位
    y_high = (diff_y_abs >> 8) & 0xFF  # y高八位
    y_low = diff_y_abs & 0xFF          # y低八位

    # 构建数据包（使用特殊标识符0x15）
    packet = bytes([
        0x78,      # 包头
        0x15,      # 特殊标识符（精确瞄准模式）
        x_high,    # x坐标差绝对值高八位
        x_low,     # x坐标差绝对值低八位
        y_high,    # y坐标差绝对值高八位
        y_low,     # y坐标差绝对值低八位
        0xFC       # 包尾
    ])

    # 通过串口发送二进制数据包
    global uart
    if uart and uart.is_initialized:
        try:
            # 使用send_bytes方法，支持发送控制
            success = uart.send_bytes(packet)
            if success:
                print(f"发送激光打开数据包: diff_x={diff_x}, diff_y={diff_y}, 包=[{', '.join(f'0x{b:02X}' for b in packet)}]")
            else:
                print(f"激光打开数据包发送被阻止或失败")
        except Exception as e:
            print(f"激光打开数据包发送失败: {e}")
    else:
        print("串口未初始化，无法发送激光打开数据包")

def send_laser_complete_packet(diff_x, diff_y):
    """
    发送激光完成模式的二进制数据包（使用特殊标识符0x25）
    数据包格式：0x78, 0x25, x高八位, x低八位, y高八位, y低八位, 0xFC

    :param diff_x: x坐标差值（有符号整数）
    :param diff_y: y坐标差值（有符号整数）
    """
    # 取绝对值并限制在16位范围内
    diff_x_abs = abs(diff_x) & 0xFFFF
    diff_y_abs = abs(diff_y) & 0xFFFF

    # 分解为高低八位
    x_high = (diff_x_abs >> 8) & 0xFF  # x高八位
    x_low = diff_x_abs & 0xFF          # x低八位
    y_high = (diff_y_abs >> 8) & 0xFF  # y高八位
    y_low = diff_y_abs & 0xFF          # y低八位

    # 构建数据包（使用特殊标识符0x25）
    packet = bytes([
        0x78,      # 包头
        0x25,      # 特殊标识符（激光完成模式）
        x_high,    # x坐标差绝对值高八位
        x_low,     # x坐标差绝对值低八位
        y_high,    # y坐标差绝对值高八位
        y_low,     # y坐标差绝对值低八位
        0xFC       # 包尾
    ])

    # 通过串口发送二进制数据包
    global uart
    if uart and uart.is_initialized:
        try:
            # 使用send_bytes方法，支持发送控制
            success = uart.send_bytes(packet)
            if success:
                print(f"发送激光完成数据包: diff_x={diff_x}, diff_y={diff_y}, 包=[{', '.join(f'0x{b:02X}' for b in packet)}]")
                # 发送完成后自动关闭串口发送功能
                uart.disable_send()
                print("激光完成，自动关闭串口发送功能")
            else:
                print(f"激光完成数据包发送被阻止或失败")
        except Exception as e:
            print(f"激光完成数据包发送失败: {e}")
    else:
        print("串口未初始化，无法发送激光完成数据包")

# --------------------------- 透视变换工具函数 ---------------------------
def perspective_transform(pts, target_width, target_height):
    """
    对四边形进行透视变换
    :param pts: 四边形顶点坐标 (4,2)
    :param target_width: 校正后宽度
    :param target_height: 校正后高度
    :return: 变换矩阵M和逆矩阵M_inv
    """
    # 顶点排序（左上→右上→右下→左下）
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    # 目标坐标
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    # 计算变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)  # 逆矩阵用于映射回原图
    return M, M_inv, src_pts

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # ========================== 工作模式宏定义接口 ==========================
    # 主要工作模式选择（用户手动设置）
    LASER_VISIBLE_MODE = True   # True: 激光点可见模式, False: 主动瞄准模式

    # 轴向解耦控制策略变量：Y轴用传统PID，X轴用动态目标点引导
    dynamic_target_x = None  # 动态X轴目标点坐标
    target_update_rate = 0.2  # 目标点更新速率（0-1之间，越大收敛越快，建议0.1-0.5）
    final_target_x = None     # 最终目标X坐标（圆心X坐标）

    # 主动瞄准策略参数
    camera_center_x = 90      # 摄像头图像中心X坐标 (180/2)
    camera_center_y = 60      # 摄像头图像中心Y坐标 (120/2)
    laser_y_offset = 0      # 激光器Y轴固定偏移角度（负值表示向下，可调节）

    # 激光打开后的自动控制逻辑参数
    laser_fire_start_time = None    # 激光打开开始时间
    laser_appear_time = None        # 激光点出现时间
    laser_control_state = "idle"    # 控制状态：idle, waiting_laser, waiting_delay, completed
    LASER_WAIT_TIMEOUT = 2.0        # 等待激光点出现的超时时间（秒）
    LASER_DELAY_TIME = 0.5          # 激光点出现后的等待时间（秒）
  

    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(180, 120, image.Format.FMT_BGR888)
    laser_detector = PurpleLaserDetector()
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("$$", "##", True)
    else:
        print("串口初始化失败")
        exit()

    # 核心参数
    min_contour_area = 200
    max_contour_area = 10000
    target_sides = 4
    
    # 透视变换与圆形参数
    corrected_width = 200    # 校正后矩形宽度
    corrected_height = 150   # 校正后矩形高度
    circle_radius = 40       # 校正后矩形内圆的半径
    circle_num_points = 12   # 圆周点数量
    
    # FPS计算初始化
    fps = 0
    last_time = time.ticks_ms()
    
    # 绘制参数（避免干扰识别区域）
    DRAW_PADDING = 10  # 绘制边距
    FPS_POSITION = (100, 15)  # FPS显示位置(右上)
    UART_STATUS_POSITION = (100, 35)  # 串口状态显示位置(右上)
    TEXT_FONT = cv2.FONT_HERSHEY_SIMPLEX
    TEXT_SCALE = 0.4
    TEXT_THICKNESS = 1
    TEXT_COLOR = (0, 255, 0)  # 绿色

    while not app.need_exit():
        # 计算FPS
        current_time = time.ticks_ms()
        if current_time - last_time > 0:
            fps = 1000.0 / (current_time - last_time)
        last_time = current_time
        
        # 读取图像
        img = cam.read()
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        output = img_cv.copy()

        # 1. 矩形检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 77, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))

        # 只保留最大的矩形
        inner_quads = []
        if quads:
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]

        # 2. 处理内框：透视变换→画圆→映射回原图
        all_circle_points = []  # 存储所有映射回原图的圆轨迹点
        for approx, area in inner_quads:
            # 提取顶点
            pts = approx.reshape(4, 2).astype(np.float32)
            
            # 计算透视变换矩阵
            M, M_inv, src_pts = perspective_transform(
                pts, corrected_width, corrected_height
            )
            
            # 生成校正后矩形内的圆形轨迹（圆心为校正后矩形的中心）
            corrected_center = (corrected_width // 2, corrected_height // 2)
            corrected_circle = generate_circle_points(
                corrected_center, circle_radius, circle_num_points
            )

            # 将校正后的圆轨迹点映射回原图
            if M_inv is not None:
                # 格式转换为opencv需要的形状 (1, N, 2)
                corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                original_points = [(int(x), int(y)) for x, y in original_points]
                all_circle_points.extend(original_points)
                
                # 绘制映射回原图的轨迹点（红色）
                for (x, y) in original_points:
                    cv2.circle(output, (x, y), 2, (0, 0, 255), -1)

        # 3. 激光检测（优先进行，避免被圆心绘制干扰）
        output, laser_points = laser_detector.detect(output)

        # 绘制内框轮廓和中心点（在激光检测之后）
        for approx, area in inner_quads:
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)
            M_moments = cv2.moments(approx)
            if M_moments["m00"] != 0:
                cx = int(M_moments["m10"] / M_moments["m00"])
                cy = int(M_moments["m01"] / M_moments["m00"])
                cv2.circle(output, (cx, cy), 2, (0, 255, 255), -1)  # 改为黄色，避免与紫色激光混淆

        # 4. 串口发送数据 - 基于宏定义的模式控制
        if all_circle_points:  # 只要检测到目标圆心就可以工作
            # 计算圆心坐标（取所有圆轨迹点的中心）
            circle_center_x = sum(x for x, y in all_circle_points) // len(all_circle_points)
            circle_center_y = sum(y for x, y in all_circle_points) // len(all_circle_points)

            if LASER_VISIBLE_MODE:
                # 模式1：激光点可见模式 - 使用轴向解耦控制策略（宏定义控制）
                # 更新最终目标X坐标（圆心X坐标）
                final_target_x = circle_center_x

                # 检查是否有激光点数据
                if laser_points:
                    # 检测到激光点，停止串口发送但继续识别
                    if uart.get_send_status():
                        uart.disable_send()
                        print("检测到激光点，停止串口发送")

                    # 对每个激光点计算坐标差（用于显示，不发送）
                    for laser_x, laser_y in laser_points:
                        # Y轴：传统PID控制，直接使用圆心坐标差
                        diff_y = laser_y - circle_center_y

                        # X轴：动态目标点引导策略
                        if dynamic_target_x is None:
                            # 首次初始化：设置动态目标点为当前激光点位置
                            dynamic_target_x = laser_x

                        # 动态更新X轴目标点：向最终目标（圆心）逐步逼近
                        target_diff = final_target_x - dynamic_target_x
                        dynamic_target_x += target_diff * target_update_rate

                        # 计算激光点与动态目标点的X轴差值
                        diff_x = laser_x - dynamic_target_x

                        # 显示调试信息（继续显示但不发送）
                        debug_text1 = f"Laser Detected - X={diff_x:+4.0f}, Y={diff_y:+4d} [UART OFF]"
                        cv2.putText(output, debug_text1, (10, 20),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255, 0, 0), 1)  # 红色表示停止发送

                        # 不再发送数据包，但继续识别和显示


            else:
                # 模式2：主动瞄准模式 - 使用主动瞄准策略（宏定义控制）
                # 让摄像头中心对准目标圆心，Y轴误差固定为0
                diff_x = circle_center_x - camera_center_x  # 摄像头X轴对准圆心
                diff_y = 0  # 主动瞄准模式下Y轴误差始终为0

                # 判断是否进入激光打开模式（X轴差值在-1到+1之间）
                laser_fire_mode = -1 <= diff_x <= 1

                # 激光打开后的自动控制逻辑
                current_time = time.ticks_ms() / 1000.0  # 转换为秒

                if laser_fire_mode and laser_control_state == "idle":
                    # 刚进入激光打开模式，开始等待激光点出现
                    laser_control_state = "waiting_laser"
                    laser_fire_start_time = current_time
                    print("激光打开模式启动，等待激光点出现...")

                elif laser_control_state == "waiting_laser":
                    # 检查是否超时
                    if current_time - laser_fire_start_time > LASER_WAIT_TIMEOUT:
                        print("等待激光点超时，重置状态")
                        laser_control_state = "idle"
                        laser_fire_start_time = None
                    # 检查激光点是否出现
                    elif laser_points:
                        print("激光点出现，开始延时等待...")
                        laser_control_state = "waiting_delay"
                        laser_appear_time = current_time

                elif laser_control_state == "waiting_delay":
                    # 等待500ms延时
                    if current_time - laser_appear_time > LASER_DELAY_TIME:
                        print("延时完成，发送激光完成数据包并关闭串口")
                        send_laser_complete_packet(int(diff_x), diff_y)
                        laser_control_state = "completed"
                        # 不退出程序，继续运行

                # 显示调试信息
                laser_status = "ON" if laser_fire_mode else "OFF"
                debug_text1 = f"Active Aim - X={diff_x:+4d}, Y={diff_y:+4d}"
                cv2.putText(output, debug_text1, (10, 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255, 0, 0), 1)

                debug_text2 = f"Laser: {laser_status}, State: {laser_control_state}"
                laser_color = (0, 255, 0) if laser_fire_mode else (255, 100, 0)  # 绿色=ON，橙色=OFF
                cv2.putText(output, debug_text2, (10, 35),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, laser_color, 1)

                # 发送二进制数据包：根据激光打开状态选择不同的发送方式
                if laser_control_state == "completed":
                    # 已完成，不再发送数据包
                    pass
                elif laser_fire_mode:
                    # 激光打开模式：使用特殊标识符0x15
                    send_laser_fire_packet(int(diff_x), diff_y)
                else:
                    # 普通主动瞄准：使用标准标识符
                    send_laser_diff_packet(int(diff_x), diff_y)

        # 在右上角显示FPS和串口状态，避免干扰主要识别区域
        cv2.putText(output, f"FPS: {fps:.1f}", FPS_POSITION,
                   TEXT_FONT, TEXT_SCALE, TEXT_COLOR, TEXT_THICKNESS)

        # 显示图像
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)    